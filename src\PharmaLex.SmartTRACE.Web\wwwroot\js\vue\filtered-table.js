﻿vueApp.component('collection-cell', {
    template: '#collection-cell-template',
    props: {
        val: {
            required: true,
            type: Array
        }
    },
    computed: {
        tipContent() {
            return `<ul>
                ${
                this.val.slice(1, this.val.length)
                    .map(x => `<li>${x}</li>`).join(' ')
                }</ul>`;
        }
    }
});
let editableCellProps = {
    props: {
        editMode: {
            type: Boolean,
            required: false,
            default: false
        },
        typeName: String,
        fieldName: String,
        rowIndex: Number,
        required: Boolean
    }
};

vueApp.component('text-cell', {
    template: '#text-cell-template',
    props: {
        val: String
    },
    mixins: [editableCellProps]
});
vueApp.component('link-cell', { template: '<td><a class="action-link" :href="href" target="_blank" v-on:click.stop>{{val}}</a></td>', props: ['val', 'href'] });
vueApp.component('multiline-cell', { template: '<td>{{val}}</td>', props: ['val'] });
vueApp.component('html-cell', { template: '<td v-html="val"></td>', props: ['val'] });
vueApp.component('number-cell', { template: '<td>{{val}}</td>', props: ['val'] });
vueApp.component('email-cell', { template: '<td>{{val}}</td>', props: ['val'] });
vueApp.component('url-cell', { template: '<td>{{val}}</td>', props: ['val'] });
vueApp.component('date-cell', { template: '<td>{{convert ? convert(val) : val}}</td>', props: ['val', 'convert'] });
vueApp.component('picklist-cell', { template: '<td>{{val}}</td>', props: ['val'] });
vueApp.component('relationship-cell', { template: '<td>{{val}}</td>', props: ['val'] });
vueApp.component('bool-cell', {
    template: '#bool-cell-template',
    props: {
        val: Boolean
    },
    mixins: [editableCellProps]
});
vueApp.component('select-filter', {
    template: '#select-filter-template',
    data: function () {
        return {
            isOpen: false,
            selectedIndex: -1,
            textField: this.config.display,
            dataField: this.config.dataKey
        };
    },
    model: {
        prop: 'value',
        event: 'input'
    },
    props: {
        value: {
            required: true,
            default: ''
        },
        config: {
            type: Object,
            required: true
        },
        filtered: {
            type: Array,
            required: true
        }
    },
    computed: {
        availableOptions: function () {
            return [...new Set(this.filtered.map(p => p[this.config.filterCollection]).flat())]
                .map(i => {
                    if (this.textField === this.dataField) {
                        return {
                            key: i === null ? '-' : i,
                            value: i === null ? '-- no value' : i
                        };
                    }

                    return {
                        key: i,
                        value: this.config.options.find(x => x[this.dataField] === i)[this.textField]
                    };
                })
                .sort((a, b) => a.value.localeCompare(b.value, 'en', { sensitivity: 'base' }));
        }
    },
    methods: {
        open() {
            this.isOpen = !this.isOpen;

            if (this.isOpen)
                this.selectedIndex = this.value ? 0 : -1;
            this.$nextTick(function () {
                this.$refs.select.focus();
            });
        },
        change(val) {
            this.$emit('update:modelValue', val);
            this.$emit('filter', { key: this.config.key });
        },
        handleClickOutside(evt) {

            let dropdownElement = Array.from(document.querySelectorAll('.table-filter-items')).find(d => d.style.display !== 'none');

            if (!this.$el.contains(evt.target) && dropdownElement && !dropdownElement.contains(evt.target)) {
                this.isOpen = false;
                this.selectedIndex = -1;
            }
        },
        onArrowDown() {
            if (this.isOpen && this.selectedIndex < this.filteredItems.length - 1) {
                this.selectedIndex = this.selectedIndex + 1;
            }
        },
        onArrowUp() {
            if (this.isOpen && this.selectedIndex > 0) {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        close() {
            this.isOpen = false;
        }
    },
    mounted: function () {
        document.addEventListener('click', this.handleClickOutside);
        window.addEventListener('resize', this.close);
    },
    destroyed: function () {
        document.removeEventListener('click', this.handleClickOutside);
        window.removeEventListener('resize', this.close);
    }
});
vueApp.component('select-multiple-filter', {
    template: '#select-multiple-filter-template',
    data: function () {
        return {
            isOpen: false,
            textField: this.config.display,
            dataField: this.config.dataKey
        };
    },
    name: 'select-multiple-filter',
    model: {
        prop: 'value',
        event: 'input'
    },
    props: {
        value: {
            required: true,
            default: ''
        },
        config: {
            type: Object,
            required: true
        },
        filtered: {
            type: Array,
            required: true
        }
    },
    computed: {
        availableOptions: function () {
            return this.value ? this.value.map(v => {
                return {
                    key: v.key,
                    value: v.value,
                    selected: v.selected
                }
            }) : [...this.config.options].map(o => {
                return {
                    key: o[this.config.dataKey],
                    value: o[this.config.display],
                    selected: o.selected
                };
            })
            .sort((a, b) => a.value.localeCompare(b.value, 'en', { sensitivity: 'base' }));
        }
    },
    methods: {
        open() {
            this.isOpen = !this.isOpen;

            this.$nextTick(function () {
                this.$refs.select.focus();
            });
        },
        change(val) {

            if (val) {
                let option = this.availableOptions.find(o => o.value === val);
                option.selected = !option.selected;

                this.$emit('input', this.availableOptions.every(o => !o.selected) ? '' : this.availableOptions);
                this.$emit('filter', { key: this.config.key });
            }
            else {
                this.$emit('update:modelValue', val);
                this.$emit('filter', { key: this.config.key });
            }

        },
        handleClickOutside(evt) {

            let dropdownElement = Array.from(document.querySelectorAll('.table-filter-items')).find(d => d.style.display !== 'none');

            if (!this.$el.contains(evt.target) && dropdownElement && !dropdownElement.contains(evt.target)) {
                this.isOpen = false;
            }
        },
        onArrowDown() {
            if (this.isOpen && this.selectedIndexes[0] < this.filteredItems.length - 1) {
                this.selectedIndexes[0] = this.selectedIndexes[0] + 1;
            }
        },
        onArrowUp() {
            if (this.isOpen && this.selectedIndexes[0] > 0) {
                this.selectedIndexes[0] = this.selectedIndexes[0] - 1;
            }
        },
        close() {
            this.isOpen = false;
        }
    },
    mounted: function () {
        document.addEventListener('click', this.handleClickOutside);
        window.addEventListener('resize', this.close);
    },
    destroyed: function () {
        document.removeEventListener('click', this.handleClickOutside);
        window.removeEventListener('resize', this.close);
    }
});
vueApp.component('search-filter', {
    template: '#search-filter-template',
    data() {
        return {
            isOpen: false
        };
    },
    model: {
        prop: 'value',
        event: 'input'
    },
    props: {
        value: {
            required: true,
            default: ''
        },
        config: {
            type: Object,
            required: true
        },
        filtered: {
            type: Array,
            required: true
        }
    },
    methods: {
        open() {
            this.isOpen = !this.isOpen;
            if (this.isOpen)
                this.$nextTick(function () {
                    this.$refs.search.focus();
                });
        },
        change(val) {
            this.$emit('update:modelValue', val);
            this.$emit('filter', { key: this.config.key });
        },
        handleClickOutside(evt) {

            let dropdownElement = Array.from(document.querySelectorAll('.table-filter-items')).find(d => d.style.display !== 'none');

            if (!this.$el.contains(evt.target) && dropdownElement && !dropdownElement.contains(evt.target)) {
                this.isOpen = false;
            }
        },
        close() {
            this.isOpen = false;
        }
    },
    mounted: function () {
        document.addEventListener('click', this.handleClickOutside);
        window.addEventListener('resize', this.close);
    },
    destroyed: function () {
        document.removeEventListener('click', this.handleClickOutside);
        window.removeEventListener('resize', this.close);
    }
});
vueApp.component('data-table-pager', {
    template: '#data-table-pager-template',
    props: {
        itemCount: Number,
        pageIndex: Number,
        pageSize: Number,
        location: {
            type: String,
            default: 'bottom'
        },
        totalItems: {
            type: Number,
            required: true
        }
    },
    computed: {
        startIndex: function () {
            return this.pageSize * this.pageIndex;
        },
        endIndex: function () {
            return Math.min(parseInt(this.startIndex) + parseInt(this.pageSize), this.itemCount);
        },
        pageCount: function () {
            let extra = this.itemCount % this.pageSize ? 1 : 0;
            return Math.floor(this.itemCount / this.pageSize) + extra;
        }
    },
    methods: {
        pageSizeChange(size) {
            this.$emit('page-size-change', +size);
        }
    }
});
vueApp.component('filtered-table', {
    template: '#filtered-table-template',
    data: function () {
        let sortConfig = this.columns.config.map(col => {
            return {
                sortKey: col.sortKey,
                sortDirection: col.sortDirection,
                sortComparer: col.sortComparer || this.items[0] ? typeof this.items[0][col.sortKey] : null
            };
        });

        return {
            editing: false,
            size: this.pageSize,
            pageIndex: 0,
            filterModel: this.filters.reduce((acc, val) => {
                acc[val.key] = '';
                return acc;
            }, {}),
            appliedFilters: {},
            sortConfig,
            sortMode: this.columns.sortMode || 'single',
            appliedSorts: sortConfig.reduce((acc, val) => {
                if (val.sortKey && val.sortDirection) {
                    acc.push({
                        key: val.sortKey,
                        comparer: this.getComparer(val.sortKey, val.sortDirection, val.sortComparer)
                    });
                }
                return acc;
            }, []),
            sortModel: this.columns.config.reduce((acc, c) => {
                if (c.sortKey)
                    acc[c.sortKey] = c.sortDirection || 0;
                return acc;
            }, {}),
            internalFilters: this.filters.map(f => {
                let fi = { ...f };

                fi.dataKey = fi.dataKey ? fi.dataKey : (fi.options[0] || {}).hasOwnProperty('id') ? 'id' : 'Id';
                fi.display = fi.display ? fi.display : (fi.options[0] || {}).hasOwnProperty('name') ? 'name' : 'Name';

                fi.convert = fi.convert || (v => v);
                fi.fn = fi.fn || (v => p => (p[fi.dataKey] === null ? '-- no value' : p[fi.dataKey]).toLowerCase().includes(v.toLowerCase()));
                return fi;
            }),
            storageKey: `${window.location.href}(${this.$attrs.id || ''})`
        };
    },
    props: {
        items: Array,
        filters: {
            type: Array,
            default: () => []
        },
        pageSize: {
            type: Number,
            default: 25
        },
        pagerLocation: {
            type: String,
            default: 'bottom'
        },
        columns: Object,
        styling: String,
        link: String,
        behavior: String,
        addurl: String,
        noRecordsMessage: {
            type: String,
            required: false,
            default: 'No matching records found'
        },
        editMode: {
            type: Boolean,
            required: false,
            default: false
        },
        typeName: String,
        noDeleteMessage: String
    },
    computed: {
        itemCount: function () {
            return this.filterItems.length;
        },
        filterItems: function () {
            this.pageIndex = 0;
            return Object.keys(this.appliedFilters).reduce((toFilter, key) => {
                return toFilter.filter(this.appliedFilters[key]);
            }, this.items);
        },
        sortItems: function () {
            return this.appliedSorts.reduce((acc, val) => {
                return acc.sort(val.comparer);
            }, this.filterItems.slice());
        },
        pageItems: function () {
            return this.editMode ? this.sortItems : this.sortItems.slice(this.size * this.pageIndex, this.size * (this.pageIndex + 1));
        },
        isFiltered() {
            return !!Object.keys(this.appliedFilters).length;
        }
    },
    methods: {
        loadState() {
            try {
                let stateString = localStorage.getItem(this.storageKey);
                let state = stateString ? JSON.parse(stateString) : { date: new Date() };
                let now = new Date();
                let stateDate = new Date(state.date);

                if (now.getDay() === stateDate.getDay() &&
                    now.getMonth() === stateDate.getMonth() &&
                    now.getYear() === stateDate.getYear()) {

                    if (state.filterModel) {
                        this.filterModel = state.filterModel;
                        Object.keys(this.filterModel).forEach(key => {
                            if (this.filterModel[key])
                                this.filter({ key });
                        });
                    }

                    if (state.sortModel) {
                        state.sortModel.forEach(s => {
                            let [key, order] = s;
                            this.sortModel[key] = order;

                            let column = this.columns.config.find(x => x.sortKey === key);
                            this.applySort(column, order);
                        });
                    }

                    this.size = state.pageSize || this.size;
                    this.$nextTick(function () {
                        this.pageIndex = state.hasOwnProperty('pageIndex') ? state.pageIndex : this.pageIndex;
                    });
                } else localStorage.removeItem(this.storageKey);
            } catch {
                localStorage.removeItem(this.storageKey);
            }
        },
        updateState(partial) {
            let stateString = localStorage.getItem(this.storageKey);
            let state = stateString ? JSON.parse(stateString) : {};

            partial = { ...partial, date: new Date() };
            localStorage.setItem(this.storageKey, JSON.stringify(Object.assign(state, partial)));
        },
        getHref(item, column) {
            if (column?.type === 'link') return item[column.dataKey];

            let [match, field] = this.link.match(/\{([a-z0-9]+)\}/i) || [];
            if (field) {
                return this.link.replace(match, item[field]);
            }

            return this.link + item[this.columns.idKey];
        },
        edit(item, config) {
            if (this.link?.length > 0) {
                let href = this.getHref(item, config);

                if (this.behavior === 'newtab') {
                    let link = document.createElement('a');
                    link.href = href;
                    link.setAttribute('target', '_blank');
                    link.click();
                } else
                    window.location.href = href;
            } else {
                this.$emit('row-clicked', item);
            }
        },
        add() {
            if (this.editMode === true) {
                let item = this.columns.config.reduce((o, col) => {
                    return {
                        ...o,
                        [col['dataKey']]: null
                    };
                }, {});
                this.editing = true;
                item.canDelete = true;
                this.items.push(item);
                this.$emit('item-added', item);
            } else {
                window.location.href = this.addurl;
            }
        },
        getComparer(key, direction = 0, comparer = 'string') {
            if (comparer === 'number')
                return (a, b) => {
                    let akey = a[key] || 0,
                        bkey = b[key] || 0;

                    if (akey === bkey) return 0;
                    if (akey > bkey) return 1 * direction;

                    return -1 * direction;
                };

            if (comparer === 'string')
                return (a, b) => {
                    let akey = a[key] || '',
                        bkey = b[key] || '';

                    return direction * (akey).localeCompare(bkey, 'en', { sensitivity: 'base' });
                };

            if (comparer === 'boolean')
                return (a, b) => {
                    let akey = a[key] ? 1 : 0,
                        bkey = b[key] ? 1 : 0;

                    return direction * (akey - bkey);
                };

            return () => 0;
        },
        sort(column) {
            if (column.sortKey) {
                let order = 0;
                let currentSort = this.sortModel[column.sortKey];

                if (!currentSort)
                    order = 1;
                else if (currentSort === 1)
                    order = -1;

                if (this.sortMode === 'single') {
                    Object.keys(this.sortModel).forEach(k => this.sortModel[k] = 0);
                }

                this.sortModel[column.sortKey] = order;
                this.applySort(column, order);
            }
        },
        applySort(column, order) {
            let sorter = this.appliedSorts.find(x => x.key === column.sortKey);
            let sortComparer = this.sortConfig.find(x => x.sortKey === column.sortKey).sortComparer;

            if (sorter) {
                sorter.comparer = this.getComparer(column.sortKey, order, sortComparer);
                this.appliedSorts = this.appliedSorts.slice();
            } else {
                if (this.sortMode !== 'single') {
                    this.appliedSorts.splice(0, 0, {
                        key: column.sortKey,
                        comparer: this.getComparer(column.sortKey, order, sortComparer)
                    });
                } else if (order) {
                    this.appliedSorts = [{
                        key: column.sortKey,
                        comparer: this.getComparer(column.sortKey, order, sortComparer)
                    }];
                }
            }

            this.updateState({
                sortModel: Object.entries(this.sortModel)
            });
        },
        filter({ key }) {
            let val = this.filterModel[key];
            this.$emit('filter', { key, val });

            if (!val && val !== false) {
                delete this.appliedFilters[key]
            } else {
                let f = this.getFilterByKey(key);
                this.appliedFilters[key] = f.fn(f.convert(val));
            }

            this.updateState({
                filterModel: this.filterModel,
                pageIndex: 0
            });
        },
        getFilterByKey(key) {
            return this.internalFilters.find(f => f.key === key);
        },
        getColumnStyle(column) {
            let s = 'white-space: nowrap; overflow: hidden; text-overflow: ellipsis;';
            if (column.type === 'bool') s += 'text-align:center;';
            return (column.style || '') + s;
        },
        setRowClass(row) {
            let classes = ['table-row'];
            if (this.link?.length > 0) {
                classes.push('selectable');
            }
            if (row[this.columns.styleKey]) {
                classes.push(row[this.columns.styleKey].toLowerCase());
            }

            return classes;
        },
        clear(stateless) {
            this.internalFilters.forEach((f, i) => {
                this.filterModel[f.key] = '';
                this.updateState({ filterModel: this.filterModel });
                delete this.appliedFilters[f.key];
            });

            this.$emit('filter');
        },
        changePage(index) {
            this.pageIndex = +index;
            this.updateState({ pageIndex: this.pageIndex });
        },
        changePageSize(size) {
            this.changePage(0);
            this.size = +size;
            this.updateState({ pageSize: this.size });

        },
        tippify() {
            tippy('.tipified', {
                //trigger: 'click',
                interactive: true,
                placement: 'auto-end',
                animateFill: false
            });
        },
        deleteItemClicked(id) {
            this.items.splice(this.items.findIndex(x => x[this.columns.idKey] === id), 1);
        }
    },
    watch: {
        editMode() {
            if (this.editMode) this.clear(true);
        }
    },
    updated() {
        this.$nextTick(function () {
            this.tippify();
        });
    },
    mounted() {
        this.tippify();
        this.loadState();
    }
});
