parameters:
  - name: packageLocation
    type: string
    default: 'drop/PharmaLex.SmartTRACE.Web.zip'
  - name: packageFaLocation
    type: string
    default: 'drop/PharmaLex.SmartTRACE.RemindersApp.zip'
  - name: environments
    type: object
    default:
      - name: 'dr'
        prefix: 'dr'
        locked: true
        azureSubscription: 'SmartTrace Production'
        SubmissionRequestStateChangedTemplateId: 'd-f8b4cdffde3d4ce8a1cfe5e3ca7d1546'
        ExternalUserLoginEmailTemplateId: 'd-b14e1dd4ee13464e932c51240586c8a7'
        ExternalUserSignUpEmailTemplateId: 'd-8f65a6bbcd0543999a6e0fdd221736ff'
        SubmissionSourceDocumentsUploadedTemplateId: 'd-c3a0983985474ce3a440a7809877e297'
        SubmissionReminderNotificationTemplateId: 'd-70f9876e4d9b4bc29194b7e84f6b3e31'

pool: 'pv-pool'

trigger: none

resources:
  pipelines:
    - pipeline: build-pipeline
      source: SmartTrace.Build
      project: SmartTrace

variables:
  platform: 'str'
  region: 'euw'

stages:
  - ${{ each env in parameters.Environments }}:
    - stage: '${{ env.name }}'
      displayName: '${{ env.name }} Deploy'
      dependsOn: '${{ env.dependsOn }}'
      variables:
        AppSettings.BuildInfo: 'Build: $(resources.pipeline.build-pipeline.runName)'
        AppSettings.BuildNumber: '$(resources.pipeline.build-pipeline.runName)'
        AppSettings.EnvironmentName: '${{ env.name }}'
        AppSettings.SubmissionRequestStateChangedTemplateId: ${{ env.SubmissionRequestStateChangedTemplateId }}
        AppSettings.ExternalUserLoginEmailTemplateId: ${{ env.ExternalUserLoginEmailTemplateId }}
        AppSettings.ExternalUserSignUpEmailTemplateId: ${{ env.ExternalUserSignUpEmailTemplateId }}
        AppSettings.SubmissionSourceDocumentsUploadedTemplateId: ${{ env.SubmissionSourceDocumentsUploadedTemplateId }}
        AppSettings.SubmissionReminderNotificationTemplateId: ${{ env.SubmissionReminderNotificationTemplateId }}
        AzureStorage.Account: '${{ variables.platform }}${{ env.prefix }}shared${{ variables.region }}'
        AzureStorage.PubAccount: '${{ variables.platform }}${{ env.prefix }}public${{ variables.region }}'
        AzureStorage.Url: 'https://${{ variables.platform }}${{ env.prefix }}shared${{ variables.region }}.blob.core.windows.net/smarttrace'
        ConnectionStrings.default: '$(ConnectionStrings--default)'
        DataFactoryPipeline.Subscription: '3f163d89-70bd-4493-9065-28ece1b2a4bd'
        DataFactoryPipeline.ResourceGroupName: 'rg-${{ variables.platform }}-${{ env.prefix }}-${{ variables.region }}'
        DataFactoryPipeline.FactoryName: '${{ variables.platform }}-${{ env.prefix }}-adf-${{ variables.region }}'
        KeyVaultName: '${{ variables.platform }}-${{ env.prefix }}-kv-${{ variables.region }}'
      jobs:
        - deployment: DeploySQL
          displayName: 'Deploy SQL dacpac ${{ env.name }}'
          environment: ${{ env.name }}
          pool: 'pv-windows-pool'
          strategy:
            runOnce:
              deploy:
                steps:
                  - task: DownloadPipelineArtifact@2
                    inputs:
                      source: 'specific'
                      project: $(resources.pipeline.build-pipeline.projectID)
                      pipeline: $(resources.pipeline.build-pipeline.pipelineName)
                      runVersion: 'specific'
                      runBranch: $(resources.pipeline.build-pipeline.sourceBranch)
                      runId: $(resources.pipeline.build-pipeline.runID)
                      path: '$(System.DefaultWorkingDirectory)'
                  - task: SqlAzureDacpacDeployment@1
                    displayName: 'Azure SQL SqlTask'
                    inputs:
                      azureSubscription: ${{ env.azureSubscription }}
                      AuthenticationType: servicePrincipal
                      ServerName: '${{ variables.platform }}-${{ env.prefix }}-sqlserver-${{ variables.region }}.database.windows.net'
                      DatabaseName: '${{ variables.platform }}-${{ env.prefix }}-default-${{ variables.region }}'
                      deployType: SqlTask
                      SqlFile: '$(System.DefaultWorkingDirectory)/drop/Migrations/migration.sql'
                      IpDetectionMethod: 'AutoDetect'
                      DeleteFirewallRule: true
        - deployment: Deploy
          displayName: 'Deploy Env: ${{ env.name }}'
          environment: ${{ env.name }}
          strategy:
            runOnce:
              deploy:
                steps:
                  - task: DownloadPipelineArtifact@2
                    inputs:
                      source: 'specific'
                      project: $(resources.pipeline.build-pipeline.projectID)
                      pipeline: $(resources.pipeline.build-pipeline.pipelineName)
                      runVersion: 'specific'
                      runBranch: $(resources.pipeline.build-pipeline.sourceBranch)
                      runId: $(resources.pipeline.build-pipeline.runID)
                      path: '$(System.DefaultWorkingDirectory)'

                  - task: AzureKeyVault@2
                    displayName: 'Azure Key Vault: ${{ variables.keyVaultName }}'
                    inputs:
                      azureSubscription: ${{ env.azureSubscription }}
                      KeyVaultName: ${{ variables.keyVaultName }}
                      RunAsPreJob: true

                  - task: FileTransform@1
                    displayName: 'File Transform: web app'
                    inputs:
                      folderPath: '$(System.DefaultWorkingDirectory)/${{ parameters.packageLocation }}'
                      fileType: json
                      targetFiles: '**/appsettings.json'

                  - task: FileTransform@1
                    displayName: 'File Transform: function app'
                    inputs:
                      folderPath: '$(System.DefaultWorkingDirectory)/${{ parameters.packageFaLocation }}'
                      fileType: json
                      targetFiles: '**/appsettings.json'

                  - task: AzureRmWebAppDeployment@4
                    displayName: Azure App Service Deploy
                    inputs:
                      ConnectionType: 'AzureRM'
                      azureSubscription: ${{ env.azureSubscription }}
                      ResourceGroupName: 'rg-${{ variables.platform }}-${{ env.prefix }}-${{ variables.region }}'
                      appType: 'webApp'
                      WebAppName: '${{ variables.platform }}-${{ env.prefix }}-as-${{ variables.region }}'
                      packageForLinux: '$(System.DefaultWorkingDirectory)/${{ parameters.packageLocation }}'

                  - task: AzureFunctionApp@2
                    displayName: 'Azure Function App Deploy'
                    inputs:
                      connectedServiceNameARM: ${{ env.azureSubscription }}
                      appType: functionApp
                      appName: '${{ variables.platform }}-${{ env.prefix }}-fa-${{ variables.region }}'
                      package: '$(System.DefaultWorkingDirectory)/${{ parameters.packageFaLocation }}'
